# Google Business Profile (GBP) Audit System

## Complete n8n Workflow for Automated GBP Audits & Lead Generation

This folder contains a comprehensive n8n workflow system that generates professional Google Business Profile audit reports for lead generation.

## 🚀 Quick Start

### 1. Import the Workflow
Copy the JSON code from `GBP_Audit_Complete_Workflow.json` and import it into your n8n instance.

### 2. Test the Workflow
Send a POST request to the webhook endpoint with this sample data:

```json
{
  "businessName": "Joe's Pizza",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "category": "restaurant",
  "phone": "******-0123",
  "website": "https://joespizza.com",
  "contactName": "<PERSON>",
  "email": "<EMAIL>",
  "contactPhone": "******-0123",
  "company": "Joe's Pizza LLC"
}
```

### 3. Expected Output
The workflow will generate a comprehensive audit report with:
- Overall GBP Health Score (0-100)
- NAP Consistency Analysis
- Competitor Benchmarking
- Local Ranking Analysis
- Actionable Recommendations
- Lead Capture Integration

## 📋 Workflow Components

### Core Nodes:
1. **Webhook Trigger** - Receives audit requests
2. **Input Validator** - Validates and processes input data
3. **Google My Business Lookup** - Simulates GMB API calls
4. **NAP Consistency Check** - Checks business data across directories
5. **Competitor Analysis** - Analyzes top 3 local competitors
6. **Local Ranking Tracker** - Tracks keyword positions
7. **Report Generator** - Creates professional HTML/PDF reports
8. **Lead Capture System** - Captures and nurtures leads

## 🔧 Configuration Required

### API Keys Needed:
- Google My Business API
- Yelp Fusion API
- SerpAPI (for ranking data)
- SendGrid/Mailgun (for email automation)
- HubSpot/CRM API (for lead management)

### Environment Variables:
```
GMB_API_KEY=your_google_my_business_api_key
YELP_API_KEY=your_yelp_fusion_api_key
SERP_API_KEY=your_serpapi_key
SENDGRID_API_KEY=your_sendgrid_api_key
HUBSPOT_API_KEY=your_hubspot_api_key
```